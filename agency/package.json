{"name": "agency", "private": true, "type": "module", "workspaces": ["frontend/app", "server"], "scripts": {"build": "pnpm run build:frontend && pnpm run build:server", "build:frontend": "react-router build", "build:server": "pnpm --filter agency-server build", "dev": "concurrently \"pnpm run dev:frontend\" \"pnpm run dev:server\"", "dev:frontend": "react-router dev", "dev:server": "pnpm --filter agency-server dev:watch", "start": "concurrently \"pnpm run start:frontend\" \"pnpm run start:server\"", "start:frontend": "react-router-serve ./build/server/index.js", "start:server": "pnpm --filter agency-server start", "typecheck": "react-router typegen && tsc && pnpm --filter agency-server build"}, "dependencies": {"@react-router/node": "^7.7.1", "@react-router/serve": "^7.7.1", "axios": "^1.11.0", "dotenv": "^16.4.5", "isbot": "^5.1.27", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.7.1"}, "devDependencies": {"@react-router/dev": "^7.7.1", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "concurrently": "^8.2.2", "nodemon": "^3.0.2", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}