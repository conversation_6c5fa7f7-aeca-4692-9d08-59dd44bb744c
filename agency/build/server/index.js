var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
import { jsx, jsxs } from "react/jsx-runtime";
import { PassThrough } from "node:stream";
import { createReadableStreamFromReadable } from "@react-router/node";
import { ServerRouter, UNSAFE_withComponentProps, Outlet, UNSAFE_withErrorBoundaryProps, isRouteErrorResponse, Meta, Links, ScrollRestoration, Scripts, useNavigate } from "react-router";
import { isbot } from "isbot";
import { renderToPipeableStream } from "react-dom/server";
import { useState } from "react";
import { <PERSON>, Card, Text, TextField, Button, BlockStack, InlineStack } from "@storeware/polaris";
const streamTimeout = 5e3;
function handleRequest(request, responseStatusCode, responseHeaders, routerContext, loadContext) {
  return new Promise((resolve, reject) => {
    let shellRendered = false;
    let userAgent = request.headers.get("user-agent");
    let readyOption = userAgent && isbot(userAgent) || routerContext.isSpaMode ? "onAllReady" : "onShellReady";
    const { pipe, abort } = renderToPipeableStream(
      /* @__PURE__ */ jsx(ServerRouter, { context: routerContext, url: request.url }),
      {
        [readyOption]() {
          shellRendered = true;
          const body = new PassThrough();
          const stream = createReadableStreamFromReadable(body);
          responseHeaders.set("Content-Type", "text/html");
          resolve(
            new Response(stream, {
              headers: responseHeaders,
              status: responseStatusCode
            })
          );
          pipe(body);
        },
        onShellError(error) {
          reject(error);
        },
        onError(error) {
          responseStatusCode = 500;
          if (shellRendered) {
            console.error(error);
          }
        }
      }
    );
    setTimeout(abort, streamTimeout + 1e3);
  });
}
const entryServer = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: handleRequest,
  streamTimeout
}, Symbol.toStringTag, { value: "Module" }));
const links = () => [{
  rel: "preconnect",
  href: "https://fonts.googleapis.com"
}, {
  rel: "preconnect",
  href: "https://fonts.gstatic.com",
  crossOrigin: "anonymous"
}, {
  rel: "stylesheet",
  href: "https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
}];
function Layout({
  children
}) {
  return /* @__PURE__ */ jsxs("html", {
    lang: "en",
    children: [/* @__PURE__ */ jsxs("head", {
      children: [/* @__PURE__ */ jsx("meta", {
        charSet: "utf-8"
      }), /* @__PURE__ */ jsx("meta", {
        name: "viewport",
        content: "width=device-width, initial-scale=1"
      }), /* @__PURE__ */ jsx(Meta, {}), /* @__PURE__ */ jsx(Links, {})]
    }), /* @__PURE__ */ jsxs("body", {
      children: [children, /* @__PURE__ */ jsx(ScrollRestoration, {}), /* @__PURE__ */ jsx(Scripts, {})]
    })]
  });
}
const root = UNSAFE_withComponentProps(function App() {
  return /* @__PURE__ */ jsx(Outlet, {});
});
const ErrorBoundary = UNSAFE_withErrorBoundaryProps(function ErrorBoundary2({
  error
}) {
  let message = "Oops!";
  let details = "An unexpected error occurred.";
  let stack;
  if (isRouteErrorResponse(error)) {
    message = error.status === 404 ? "404" : "Error";
    details = error.status === 404 ? "The requested page could not be found." : error.statusText || details;
  }
  return /* @__PURE__ */ jsxs("main", {
    className: "pt-16 p-4 container mx-auto",
    children: [/* @__PURE__ */ jsx("h1", {
      children: message
    }), /* @__PURE__ */ jsx("p", {
      children: details
    }), stack]
  });
});
const route0 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  ErrorBoundary,
  Layout,
  default: root,
  links
}, Symbol.toStringTag, { value: "Module" }));
const API_BASE_URL = "http://localhost:8001";
class ApiClient {
  constructor(baseUrl = API_BASE_URL) {
    __publicField(this, "baseUrl");
    this.baseUrl = baseUrl;
  }
  async request(endpoint, options = {}) {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      const response = await fetch(url, {
        headers: {
          "Content-Type": "application/json",
          ...options.headers
        },
        ...options
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        return {
          error: errorData.message || `HTTP ${response.status}: ${response.statusText}`
        };
      }
      const data = await response.json();
      return { data };
    } catch (error) {
      return {
        error: error instanceof Error ? error.message : "An unknown error occurred"
      };
    }
  }
  async get(endpoint) {
    return this.request(endpoint, { method: "GET" });
  }
  async post(endpoint, body) {
    return this.request(endpoint, {
      method: "POST",
      body: body ? JSON.stringify(body) : void 0
    });
  }
  // Demo API methods
  async getDemoData() {
    return this.get("/v1/demo/");
  }
  async getHealthCheck() {
    return this.get("/v1/demo/health");
  }
  async getServerInfo() {
    return this.get("/");
  }
}
const apiClient = new ApiClient();
function ApiDemo() {
  const [serverInfo, setServerInfo] = useState({
    loading: false,
    data: null,
    error: null
  });
  const [demoData, setDemoData] = useState({
    loading: false,
    data: null,
    error: null
  });
  const [healthCheck, setHealthCheck] = useState({
    loading: false,
    data: null,
    error: null
  });
  const fetchServerInfo = async () => {
    setServerInfo({ loading: true, data: null, error: null });
    const response = await apiClient.getServerInfo();
    setServerInfo({
      loading: false,
      data: response.data,
      error: response.error || null
    });
  };
  const fetchDemoData = async () => {
    setDemoData({ loading: true, data: null, error: null });
    const response = await apiClient.getDemoData();
    setDemoData({
      loading: false,
      data: response.data,
      error: response.error || null
    });
  };
  const fetchHealthCheck = async () => {
    setHealthCheck({ loading: true, data: null, error: null });
    const response = await apiClient.getHealthCheck();
    setHealthCheck({
      loading: false,
      data: response.data,
      error: response.error || null
    });
  };
  const renderApiSection = (title, state, onFetch) => /* @__PURE__ */ jsxs("div", { style: { marginBottom: "2rem", padding: "1rem", border: "1px solid #ddd", borderRadius: "8px" }, children: [
    /* @__PURE__ */ jsx("h3", { style: { marginTop: 0 }, children: title }),
    /* @__PURE__ */ jsx(
      "button",
      {
        onClick: onFetch,
        disabled: state.loading,
        style: {
          padding: "0.5rem 1rem",
          backgroundColor: state.loading ? "#ccc" : "#007bff",
          color: "white",
          border: "none",
          borderRadius: "4px",
          cursor: state.loading ? "not-allowed" : "pointer",
          marginBottom: "1rem"
        },
        children: state.loading ? "Loading..." : "Fetch Data"
      }
    ),
    state.error && /* @__PURE__ */ jsxs("div", { style: { color: "red", marginBottom: "1rem" }, children: [
      /* @__PURE__ */ jsx("strong", { children: "Error:" }),
      " ",
      state.error
    ] }),
    state.data && /* @__PURE__ */ jsx("pre", { style: {
      backgroundColor: "#f8f9fa",
      padding: "1rem",
      borderRadius: "4px",
      overflow: "auto",
      fontSize: "0.875rem"
    }, children: JSON.stringify(state.data, null, 2) })
  ] });
  return /* @__PURE__ */ jsxs("div", { style: { maxWidth: "800px", margin: "0 auto", padding: "2rem" }, children: [
    /* @__PURE__ */ jsx("h2", { children: "Frontend-Backend Communication Demo" }),
    /* @__PURE__ */ jsx("p", { children: "This demo shows the frontend communicating with the new Express server." }),
    renderApiSection("Server Info (GET /)", serverInfo, fetchServerInfo),
    renderApiSection("Demo Data (GET /v1/demo/)", demoData, fetchDemoData),
    renderApiSection("Health Check (GET /v1/demo/health)", healthCheck, fetchHealthCheck)
  ] });
}
const logoDark = "/assets/logo-dark-pX2395Y0.svg";
const logoLight = "/assets/logo-light-CVbx2LBR.svg";
function Welcome() {
  return /* @__PURE__ */ jsx("main", { className: "flex items-center justify-center pt-16 pb-4", children: /* @__PURE__ */ jsxs("div", { className: "flex-1 flex flex-col items-center gap-16 min-h-0", children: [
    /* @__PURE__ */ jsx("header", { className: "flex flex-col items-center gap-9", children: /* @__PURE__ */ jsxs("div", { className: "w-[500px] max-w-[100vw] p-4", children: [
      /* @__PURE__ */ jsx(
        "img",
        {
          src: logoLight,
          alt: "React Router",
          className: "block w-full dark:hidden"
        }
      ),
      /* @__PURE__ */ jsx(
        "img",
        {
          src: logoDark,
          alt: "React Router",
          className: "hidden w-full dark:block"
        }
      )
    ] }) }),
    /* @__PURE__ */ jsx("div", { className: "max-w-[300px] w-full space-y-6 px-4", children: /* @__PURE__ */ jsxs("nav", { className: "rounded-3xl border border-gray-200 p-6 dark:border-gray-700 space-y-4", children: [
      /* @__PURE__ */ jsx("p", { className: "leading-6 text-gray-700 dark:text-gray-200 text-center", children: "What's next?" }),
      /* @__PURE__ */ jsx("ul", { children: resources.map(({ href, text, icon }) => /* @__PURE__ */ jsx("li", { children: /* @__PURE__ */ jsxs(
        "a",
        {
          className: "group flex items-center gap-3 self-stretch p-3 leading-normal text-blue-700 hover:underline dark:text-blue-500",
          href,
          target: "_blank",
          rel: "noreferrer",
          children: [
            icon,
            text
          ]
        }
      ) }, href)) })
    ] }) })
  ] }) });
}
const resources = [
  {
    href: "https://reactrouter.com/docs",
    text: "React Router Docs",
    icon: /* @__PURE__ */ jsx(
      "svg",
      {
        xmlns: "http://www.w3.org/2000/svg",
        width: "24",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none",
        className: "stroke-gray-600 group-hover:stroke-current dark:stroke-gray-300",
        children: /* @__PURE__ */ jsx(
          "path",
          {
            d: "M9.99981 10.0751V9.99992M17.4688 17.4688C15.889 19.0485 11.2645 16.9853 7.13958 12.8604C3.01467 8.73546 0.951405 4.11091 2.53116 2.53116C4.11091 0.951405 8.73546 3.01467 12.8604 7.13958C16.9853 11.2645 19.0485 15.889 17.4688 17.4688ZM2.53132 17.4688C0.951566 15.8891 3.01483 11.2645 7.13974 7.13963C11.2647 3.01471 15.8892 0.951453 17.469 2.53121C19.0487 4.11096 16.9854 8.73551 12.8605 12.8604C8.73562 16.9853 4.11107 19.0486 2.53132 17.4688Z",
            strokeWidth: "1.5",
            strokeLinecap: "round"
          }
        )
      }
    )
  },
  {
    href: "https://rmx.as/discord",
    text: "Join Discord",
    icon: /* @__PURE__ */ jsx(
      "svg",
      {
        xmlns: "http://www.w3.org/2000/svg",
        width: "24",
        height: "20",
        viewBox: "0 0 24 20",
        fill: "none",
        className: "stroke-gray-600 group-hover:stroke-current dark:stroke-gray-300",
        children: /* @__PURE__ */ jsx(
          "path",
          {
            d: "M15.0686 1.25995L14.5477 1.17423L14.2913 1.63578C14.1754 1.84439 14.0545 2.08275 13.9422 2.31963C12.6461 2.16488 11.3406 2.16505 10.0445 2.32014C9.92822 2.08178 9.80478 1.84975 9.67412 1.62413L9.41449 1.17584L8.90333 1.25995C7.33547 1.51794 5.80717 1.99419 4.37748 2.66939L4.19 2.75793L4.07461 2.93019C1.23864 7.16437 0.46302 11.3053 0.838165 15.3924L0.868838 15.7266L1.13844 15.9264C2.81818 17.1714 4.68053 18.1233 6.68582 18.719L7.18892 18.8684L7.50166 18.4469C7.96179 17.8268 8.36504 17.1824 8.709 16.4944L8.71099 16.4904C10.8645 17.0471 13.128 17.0485 15.2821 16.4947C15.6261 17.1826 16.0293 17.8269 16.4892 18.4469L16.805 18.8725L17.3116 18.717C19.3056 18.105 21.1876 17.1751 22.8559 15.9238L23.1224 15.724L23.1528 15.3923C23.5873 10.6524 22.3579 6.53306 19.8947 2.90714L19.7759 2.73227L19.5833 2.64518C18.1437 1.99439 16.6386 1.51826 15.0686 1.25995ZM16.6074 10.7755L16.6074 10.7756C16.5934 11.6409 16.0212 12.1444 15.4783 12.1444C14.9297 12.1444 14.3493 11.6173 14.3493 10.7877C14.3493 9.94885 14.9378 9.41192 15.4783 9.41192C16.0471 9.41192 16.6209 9.93851 16.6074 10.7755ZM8.49373 12.1444C7.94513 12.1444 7.36471 11.6173 7.36471 10.7877C7.36471 9.94885 7.95323 9.41192 8.49373 9.41192C9.06038 9.41192 9.63892 9.93712 9.6417 10.7815C9.62517 11.6239 9.05462 12.1444 8.49373 12.1444Z",
            strokeWidth: "1.5"
          }
        )
      }
    )
  }
];
function meta$2({}) {
  return [{
    title: "Agency App - Frontend & Backend Demo"
  }, {
    name: "description",
    content: "Welcome to the restructured Agency App!"
  }];
}
const home = UNSAFE_withComponentProps(function Home() {
  return /* @__PURE__ */ jsxs("div", {
    children: [/* @__PURE__ */ jsx(Welcome, {}), /* @__PURE__ */ jsx(ApiDemo, {})]
  });
});
const route1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: home,
  meta: meta$2
}, Symbol.toStringTag, { value: "Module" }));
function meta$1({}) {
  return [{
    title: "StoreSEO Agency - Register"
  }, {
    name: "description",
    content: "Create your StoreSEO Agency account"
  }];
}
const register = UNSAFE_withComponentProps(function Register() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    email: "",
    password: ""
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const validateForm = () => {
    const newErrors = {};
    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }
    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 6) {
      newErrors.password = "Password must be at least 6 characters long";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  const handleInputChange = (field) => (value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }));
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: void 0
      }));
    }
  };
  const handleSubmit = async (event) => {
    event.preventDefault();
    if (!validateForm()) {
      return;
    }
    setIsSubmitting(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1e3));
      navigate("/dashboard");
    } catch (error) {
      console.error("Registration failed:", error);
    } finally {
      setIsSubmitting(false);
    }
  };
  return /* @__PURE__ */ jsx("div", {
    className: "min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",
    children: /* @__PURE__ */ jsx("div", {
      className: "max-w-lg w-full space-y-8",
      children: /* @__PURE__ */ jsx(Box, {
        children: /* @__PURE__ */ jsx(Card, {
          children: /* @__PURE__ */ jsxs("form", {
            onSubmit: handleSubmit,
            children: [/* @__PURE__ */ jsx(Text, {
              as: "h1",
              variant: "headingLg",
              alignment: "center",
              children: "Create Your Account"
            }), /* @__PURE__ */ jsx(Text, {
              as: "p",
              variant: "bodyMd",
              tone: "subdued",
              alignment: "center",
              children: "Join StoreSEO Agency to get started"
            }), /* @__PURE__ */ jsxs(Box, {
              paddingBlockStart: "400",
              paddingBlockEnd: "400",
              children: [/* @__PURE__ */ jsx(TextField, {
                label: "Email",
                type: "email",
                value: formData.email,
                onChange: handleInputChange("email"),
                error: errors.email,
                autoComplete: "email",
                placeholder: "Enter your email address",
                disabled: isSubmitting
              }), /* @__PURE__ */ jsx(TextField, {
                label: "Password",
                type: "password",
                value: formData.password,
                onChange: handleInputChange("password"),
                error: errors.password,
                autoComplete: "new-password",
                placeholder: "Create a secure password",
                disabled: isSubmitting
              })]
            }), /* @__PURE__ */ jsx(Button, {
              variant: "primary",
              size: "large",
              fullWidth: true,
              submit: true,
              loading: isSubmitting,
              disabled: isSubmitting,
              children: isSubmitting ? "Creating Account..." : "Register"
            })]
          })
        })
      })
    })
  });
});
const route2 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: register,
  meta: meta$1
}, Symbol.toStringTag, { value: "Module" }));
function meta({}) {
  return [{
    title: "StoreSEO Agency - Dashboard"
  }, {
    name: "description",
    content: "Your StoreSEO Agency dashboard"
  }];
}
const dashboard = UNSAFE_withComponentProps(function Dashboard() {
  const navigate = useNavigate();
  const handleLogout = () => {
    navigate("/auth/register");
  };
  return /* @__PURE__ */ jsxs("div", {
    className: "min-h-screen bg-gray-50",
    children: [/* @__PURE__ */ jsx("div", {
      className: "bg-white shadow",
      children: /* @__PURE__ */ jsx("div", {
        className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
        children: /* @__PURE__ */ jsxs("div", {
          className: "flex justify-between items-center py-6",
          children: [/* @__PURE__ */ jsx(Text, {
            as: "h1",
            variant: "headingLg",
            children: "StoreSEO Agency Dashboard"
          }), /* @__PURE__ */ jsx(Button, {
            variant: "tertiary",
            onClick: handleLogout,
            children: "Logout"
          })]
        })
      })
    }), /* @__PURE__ */ jsx("div", {
      className: "max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",
      children: /* @__PURE__ */ jsx("div", {
        className: "px-4 py-6 sm:px-0",
        children: /* @__PURE__ */ jsxs(BlockStack, {
          gap: "600",
          children: [/* @__PURE__ */ jsx(Card, {
            children: /* @__PURE__ */ jsxs(BlockStack, {
              gap: "400",
              children: [/* @__PURE__ */ jsx(Text, {
                as: "h2",
                variant: "headingMd",
                children: "Welcome to StoreSEO Agency! 🎉"
              }), /* @__PURE__ */ jsx(Text, {
                as: "p",
                variant: "bodyMd",
                children: "Congratulations! You have successfully created your account and are now logged into your dashboard. This is a demo dashboard to showcase the registration flow."
              })]
            })
          }), /* @__PURE__ */ jsxs("div", {
            className: "grid grid-cols-1 md:grid-cols-3 gap-6",
            children: [/* @__PURE__ */ jsx(Card, {
              children: /* @__PURE__ */ jsxs(BlockStack, {
                gap: "200",
                children: [/* @__PURE__ */ jsx(Text, {
                  as: "h3",
                  variant: "headingSm",
                  tone: "subdued",
                  children: "Total Projects"
                }), /* @__PURE__ */ jsx(Text, {
                  as: "p",
                  variant: "heading2xl",
                  children: "0"
                }), /* @__PURE__ */ jsx(Text, {
                  as: "p",
                  variant: "bodySm",
                  tone: "subdued",
                  children: "Start by creating your first project"
                })]
              })
            }), /* @__PURE__ */ jsx(Card, {
              children: /* @__PURE__ */ jsxs(BlockStack, {
                gap: "200",
                children: [/* @__PURE__ */ jsx(Text, {
                  as: "h3",
                  variant: "headingSm",
                  tone: "subdued",
                  children: "SEO Score"
                }), /* @__PURE__ */ jsx(Text, {
                  as: "p",
                  variant: "heading2xl",
                  children: "--"
                }), /* @__PURE__ */ jsx(Text, {
                  as: "p",
                  variant: "bodySm",
                  tone: "subdued",
                  children: "No data available yet"
                })]
              })
            }), /* @__PURE__ */ jsx(Card, {
              children: /* @__PURE__ */ jsxs(BlockStack, {
                gap: "200",
                children: [/* @__PURE__ */ jsx(Text, {
                  as: "h3",
                  variant: "headingSm",
                  tone: "subdued",
                  children: "Active Campaigns"
                }), /* @__PURE__ */ jsx(Text, {
                  as: "p",
                  variant: "heading2xl",
                  children: "0"
                }), /* @__PURE__ */ jsx(Text, {
                  as: "p",
                  variant: "bodySm",
                  tone: "subdued",
                  children: "Ready to launch your first campaign"
                })]
              })
            })]
          }), /* @__PURE__ */ jsx(Card, {
            children: /* @__PURE__ */ jsxs(BlockStack, {
              gap: "400",
              children: [/* @__PURE__ */ jsx(Text, {
                as: "h2",
                variant: "headingMd",
                children: "Quick Actions"
              }), /* @__PURE__ */ jsxs("div", {
                className: "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",
                children: [/* @__PURE__ */ jsx(Button, {
                  variant: "primary",
                  fullWidth: true,
                  children: "Create Project"
                }), /* @__PURE__ */ jsx(Button, {
                  variant: "secondary",
                  fullWidth: true,
                  children: "SEO Analysis"
                }), /* @__PURE__ */ jsx(Button, {
                  variant: "secondary",
                  fullWidth: true,
                  children: "Keyword Research"
                }), /* @__PURE__ */ jsx(Button, {
                  variant: "secondary",
                  fullWidth: true,
                  children: "View Reports"
                })]
              })]
            })
          }), /* @__PURE__ */ jsx(Card, {
            children: /* @__PURE__ */ jsxs(BlockStack, {
              gap: "400",
              children: [/* @__PURE__ */ jsx(Text, {
                as: "h2",
                variant: "headingMd",
                children: "Getting Started"
              }), /* @__PURE__ */ jsxs(BlockStack, {
                gap: "300",
                children: [/* @__PURE__ */ jsxs("div", {
                  className: "flex items-start space-x-3",
                  children: [/* @__PURE__ */ jsx("div", {
                    className: "flex-shrink-0",
                    children: /* @__PURE__ */ jsx("div", {
                      className: "w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center",
                      children: /* @__PURE__ */ jsx(Text, {
                        as: "span",
                        variant: "bodySm",
                        children: "1"
                      })
                    })
                  }), /* @__PURE__ */ jsx("div", {
                    className: "flex-1",
                    children: /* @__PURE__ */ jsxs(Text, {
                      as: "p",
                      variant: "bodyMd",
                      children: [/* @__PURE__ */ jsx("strong", {
                        children: "Set up your profile:"
                      }), " Complete your account information and preferences"]
                    })
                  })]
                }), /* @__PURE__ */ jsxs("div", {
                  className: "flex items-start space-x-3",
                  children: [/* @__PURE__ */ jsx("div", {
                    className: "flex-shrink-0",
                    children: /* @__PURE__ */ jsx("div", {
                      className: "w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center",
                      children: /* @__PURE__ */ jsx(Text, {
                        as: "span",
                        variant: "bodySm",
                        children: "2"
                      })
                    })
                  }), /* @__PURE__ */ jsx("div", {
                    className: "flex-1",
                    children: /* @__PURE__ */ jsxs(Text, {
                      as: "p",
                      variant: "bodyMd",
                      children: [/* @__PURE__ */ jsx("strong", {
                        children: "Create your first project:"
                      }), " Add your website and start optimizing"]
                    })
                  })]
                }), /* @__PURE__ */ jsxs("div", {
                  className: "flex items-start space-x-3",
                  children: [/* @__PURE__ */ jsx("div", {
                    className: "flex-shrink-0",
                    children: /* @__PURE__ */ jsx("div", {
                      className: "w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center",
                      children: /* @__PURE__ */ jsx(Text, {
                        as: "span",
                        variant: "bodySm",
                        children: "3"
                      })
                    })
                  }), /* @__PURE__ */ jsx("div", {
                    className: "flex-1",
                    children: /* @__PURE__ */ jsxs(Text, {
                      as: "p",
                      variant: "bodyMd",
                      children: [/* @__PURE__ */ jsx("strong", {
                        children: "Run your first SEO analysis:"
                      }), " Get insights and recommendations"]
                    })
                  })]
                })]
              }), /* @__PURE__ */ jsx(InlineStack, {
                align: "start",
                children: /* @__PURE__ */ jsx(Button, {
                  variant: "primary",
                  children: "Get Started"
                })
              })]
            })
          })]
        })
      })
    })]
  });
});
const route3 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: dashboard,
  meta
}, Symbol.toStringTag, { value: "Module" }));
const serverManifest = { "entry": { "module": "/assets/entry.client-BFM3mLu-.js", "imports": ["/assets/chunk-C37GKA54-BAI9agUh.js", "/assets/index-BCQUowBv.js"], "css": [] }, "routes": { "root": { "id": "root", "parentId": void 0, "path": "", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasClientMiddleware": false, "hasErrorBoundary": true, "module": "/assets/root-CgBxNsbv.js", "imports": ["/assets/chunk-C37GKA54-BAI9agUh.js", "/assets/index-BCQUowBv.js"], "css": ["/assets/root-CwhkHDoq.css"], "clientActionModule": void 0, "clientLoaderModule": void 0, "clientMiddlewareModule": void 0, "hydrateFallbackModule": void 0 }, "routes/home": { "id": "routes/home", "parentId": "root", "path": void 0, "index": true, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasClientMiddleware": false, "hasErrorBoundary": false, "module": "/assets/home-hlqdTkJp.js", "imports": ["/assets/chunk-C37GKA54-BAI9agUh.js"], "css": [], "clientActionModule": void 0, "clientLoaderModule": void 0, "clientMiddlewareModule": void 0, "hydrateFallbackModule": void 0 }, "routes/auth/register": { "id": "routes/auth/register", "parentId": "root", "path": "auth/register", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasClientMiddleware": false, "hasErrorBoundary": false, "module": "/assets/register-7eLNVcIq.js", "imports": ["/assets/chunk-C37GKA54-BAI9agUh.js", "/assets/index-C9EGKWKr.js", "/assets/index-BCQUowBv.js"], "css": [], "clientActionModule": void 0, "clientLoaderModule": void 0, "clientMiddlewareModule": void 0, "hydrateFallbackModule": void 0 }, "routes/dashboard": { "id": "routes/dashboard", "parentId": "root", "path": "dashboard", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasClientMiddleware": false, "hasErrorBoundary": false, "module": "/assets/dashboard-CdxOTKf6.js", "imports": ["/assets/chunk-C37GKA54-BAI9agUh.js", "/assets/index-C9EGKWKr.js", "/assets/index-BCQUowBv.js"], "css": [], "clientActionModule": void 0, "clientLoaderModule": void 0, "clientMiddlewareModule": void 0, "hydrateFallbackModule": void 0 } }, "url": "/assets/manifest-68d31fe9.js", "version": "68d31fe9", "sri": void 0 };
const assetsBuildDirectory = "build/client";
const basename = "/";
const future = { "unstable_middleware": false, "unstable_optimizeDeps": false, "unstable_splitRouteModules": false, "unstable_subResourceIntegrity": false, "unstable_viteEnvironmentApi": false };
const ssr = true;
const isSpaMode = false;
const prerender = [];
const routeDiscovery = { "mode": "lazy", "manifestPath": "/__manifest" };
const publicPath = "/";
const entry = { module: entryServer };
const routes = {
  "root": {
    id: "root",
    parentId: void 0,
    path: "",
    index: void 0,
    caseSensitive: void 0,
    module: route0
  },
  "routes/home": {
    id: "routes/home",
    parentId: "root",
    path: void 0,
    index: true,
    caseSensitive: void 0,
    module: route1
  },
  "routes/auth/register": {
    id: "routes/auth/register",
    parentId: "root",
    path: "auth/register",
    index: void 0,
    caseSensitive: void 0,
    module: route2
  },
  "routes/dashboard": {
    id: "routes/dashboard",
    parentId: "root",
    path: "dashboard",
    index: void 0,
    caseSensitive: void 0,
    module: route3
  }
};
export {
  serverManifest as assets,
  assetsBuildDirectory,
  basename,
  entry,
  future,
  isSpaMode,
  prerender,
  publicPath,
  routeDiscovery,
  routes,
  ssr
};
