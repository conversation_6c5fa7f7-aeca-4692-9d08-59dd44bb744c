{"name": "agency-frontend", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@react-router/node": "^7.7.1", "@react-router/serve": "^7.7.1", "@storeware/polaris": "github:Storeware-Apps/storeware-polaris#v0.1.0", "isbot": "^5.1.27", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.7.1"}, "devDependencies": {"@react-router/dev": "^7.7.1", "@tailwindcss/vite": "^4.1.4", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4.1.4", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}